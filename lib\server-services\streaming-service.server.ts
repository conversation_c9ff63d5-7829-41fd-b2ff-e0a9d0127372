// lib/server/streaming-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios, { AxiosResponse } from 'axios'
import {
  VideoGroupType,
  VideoSubGroupType,
  VideoListType,
  StudentVideoListType,
  ApiResponse
} from '@/types/video-types'
import {
  fetchYouTubeVideoInfoWithAPI,
  extractYouTubeVideoId,
  isValidVideoLength
} from '@/lib/utils/youtube'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  preLoginUrl: `${API_BASE_URL}/prelogin/videos/all`,
  videoGroupUrl: `${API_BASE_URL}/learn/videos/groups`,
  videoSubGroupUrl: `${API_BASE_URL}/learn/videos/subgroups`,
  videoListUrl: `${API_BASE_URL}/learn/videos/all`,
  videoLinkUrl: `${API_BASE_URL}/learn/videos/`,
  ratingAndCommentsUrl: `${API_BASE_URL}/learn/video/`,
  studentVideoListUrl: `${API_BASE_URL}/testimony/videos/all`,
  studentVideoLinkUrl: `${API_BASE_URL}/testimony/videos/`,
  reviewVideoListUrl: `${API_BASE_URL}/review/videos/all`,
  reviewVideoLinkUrl: `${API_BASE_URL}/review/videos/`,
  trialVideoListUrl: `${API_BASE_URL}/learn/sample/videos/all`,
  trialVideoLinkUrl: `${API_BASE_URL}/learn/sample/videos/`,
  workshopVideoListUrl: `${API_BASE_URL}/learn/workshop/videos/all`,
  workshopVideoLinkUrl: `${API_BASE_URL}/learn/workshop/videos/`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

/**
 * Get user email from cookies
 */
const getUserEmail = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_USR')?.value || ''
}

/**
 * Enhance video list with YouTube durations if available
 */
const enhanceVideoListWithYouTubeDurations = async (
  videoList: StudentVideoListType[]
): Promise<StudentVideoListType[]> => {
  const apiKey = process.env.YOUTUBE_API_KEY

  if (!apiKey) {
    return videoList // Return original list if no API key
  }

  const enhancedVideos = await Promise.all(
    videoList.map(async (video) => {
      // Only process YouTube videos without valid duration
      if (['2', '6'].includes(video.video_type) && video.link && !isValidVideoLength(video.length)) {
        try {
          const videoId = extractYouTubeVideoId(video.link) || video.link
          const videoInfo = await fetchYouTubeVideoInfoWithAPI(videoId, apiKey)

          if (videoInfo?.duration) {
            return {
              ...video,
              length: videoInfo.duration
            }
          }
        } catch (error) {
          console.error(`Error fetching YouTube duration for video ${video.video_id}:`, error)
        }
      }

      return video
    })
  )

  return enhancedVideos
}

export class ServerStreamingService {
  /**
   * Get review video list
   */
  static async getReviewVideoList(): Promise<StudentVideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login') // Redirect to login if not authenticated
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(ENDPOINTS.reviewVideoListUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      // Enhance video list with YouTube durations
      const enhancedVideoList = await enhanceVideoListWithYouTubeDurations(response.data)
      return enhancedVideoList
    } catch (error) {
      console.error('Error fetching review video list:', error)
      throw error
    }
  }

  /**
   * Get pre-login video list (doesn't require authentication)
   */
  static async getPreLoginVideoList(): Promise<VideoListType[]> {
    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        ENDPOINTS.preLoginUrl
      )
      return response.data
    } catch (error) {
      console.error('Error fetching pre-login video list:', error)
      throw error
    }
  }

  /**
   * Get video groups
   */
  static async getVideoGroups(): Promise<VideoGroupType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<VideoGroupType[]> = await axios.get(
        ENDPOINTS.videoGroupUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video groups:', error)
      throw error
    }
  }

  /**
   * Get video sub-groups
   */
  static async getVideoSubGroups(): Promise<VideoSubGroupType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<VideoSubGroupType[]> = await axios.get(
        ENDPOINTS.videoSubGroupUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video sub-groups:', error)
      throw error
    }
  }

  /**
   * Get video list
   */
  static async getVideoList(): Promise<VideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        ENDPOINTS.videoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video list:', error)
      throw error
    }
  }

  /**
   * Get videos of a sub-group
   */
  static async getVideosOfASubGroup(
    subGroupId: string
  ): Promise<VideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.videoSubGroupUrl}/${subGroupId}/list`

    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching videos of sub-group ${subGroupId}:`, error)
      throw error
    }
  }

  /**
   * Get video link
   */
  static async getVideoLink(videoId: string): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.videoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get video notes
   */
  static async getVideoNotes(videoId: string): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.videoLinkUrl}${videoId}/dl-notes`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching video notes for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get video comments list
   */
  static async getVideoCommentsList(videoId: string): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comments`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching video comments for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get testimony video list
   */
  static async getTestimonyVideoList(): Promise<StudentVideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.studentVideoListUrl,
        createAuthHeaders(token)
      )

      // Enhance video list with YouTube durations
      const enhancedVideoList = await enhanceVideoListWithYouTubeDurations(response.data)
      return enhancedVideoList
    } catch (error) {
      console.error('Error fetching testimony video list:', error)
      throw error
    }
  }

  /**
   * Get testimony video link
   */
  static async getTestimonyVideoLink(videoId: string): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.studentVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(
        `Error fetching testimony video link for ${videoId}:`,
        error
      )
      throw error
    }
  }

  /**
   * Get trial video list
   */
  static async getTrialVideoList(): Promise<StudentVideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.trialVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching trial video list:', error)
      throw error
    }
  }

  /**
   * Get trial video link
   */
  static async getTrialVideoLink(videoId: string): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.trialVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching trial video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get workshop video list
   */
  static async getWorkshopVideoList(): Promise<StudentVideoListType[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.workshopVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching workshop video list:', error)
      throw error
    }
  }

  /**
   * Get workshop video link
   */
  static async getWorkshopVideoLink(videoId: string): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.workshopVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching workshop video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post video rating
   */
  static async postVideoRating(
    videoId: string,
    rating_given: number
  ): Promise<ApiResponse<any>> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const email = getUserEmail()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/rating`

    try {
      const response: AxiosResponse<ApiResponse<any>> = await axios.post(
        url,
        { email, rating_given },
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting rating for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get student video rating
   */
  static async getStudentVideoRating(videoId: string): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const email = getUserEmail()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/rating/${email}/check`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching student rating for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post video comment
   */
  static async postVideoComment(
    videoId: string,
    comment_text: string,
    name?: string
  ): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const email = getUserEmail()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comment`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        { email, name: name || email, comment_text },
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting comment for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post comment reply
   */
  static async postCommentReply(
    videoId: string,
    commentId: string,
    reply_text: string,
    name?: string
  ): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/login')
    }

    const token = await getServerSideToken()
    const email = getUserEmail()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comment/${commentId}/reply`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        { email, name: name || email, reply_text },
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting reply to comment ${commentId}:`, error)
      throw error
    }
  }
}
