# YouTube Integration

This document explains how the YouTube video duration integration works in the Quant Masters application.

## Problem

In the original Angular application, YouTube videos were embedded directly as iframes, which automatically displayed the correct video duration. However, in the Next.js version, videos were showing as cards with thumbnails, and the video duration was coming from the API as "00:00 to 00:00" or empty strings.

## Solution

We implemented a multi-layered solution to fetch and display accurate YouTube video durations:

### 1. YouTube Utility Functions (`lib/utils/youtube.ts`)

- **`formatDuration(seconds)`**: Converts seconds to MM:SS or HH:MM:SS format
- **`parseISO8601Duration(duration)`**: Parses YouTube's ISO 8601 duration format (PT4M13S)
- **`extractYouTubeVideoId(url)`**: Extracts video ID from various YouTube URL formats
- **`getYouTubeThumbnail(videoId)`**: Generates YouTube thumbnail URLs
- **`isValidVideoLength(length)`**: Checks if a video length string is valid
- **`fetchYouTubeVideoInfoWithAPI(videoId, apiKey)`**: Fetches video info using YouTube Data API v3

### 2. API Route (`app/api/youtube/duration/route.ts`)

A Next.js API route that:
- Accepts a YouTube video ID as a query parameter
- Uses the YouTube Data API v3 to fetch video metadata
- Returns duration, title, and thumbnail information
- Provides fallbacks when API key is not configured

### 3. Server-Side Enhancement (`lib/server-services/streaming-service.server.ts`)

- **`enhanceVideoListWithYouTubeDurations()`**: Enhances video lists with YouTube durations
- Automatically fetches YouTube durations when videos are loaded server-side
- Only processes YouTube videos (types '2' and '6') that don't have valid durations

### 4. Client-Side Component (`components/student-exp/video-card.tsx`)

- Displays video duration from API data when available
- Falls back to fetching duration via the API route for YouTube videos
- Shows loading states and appropriate placeholders
- Uses YouTube thumbnails for better visual consistency

## Setup

### 1. Get YouTube Data API v3 Key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the YouTube Data API v3
4. Create credentials (API Key)
5. Restrict the API key to YouTube Data API v3 for security

### 2. Configure Environment Variables

Add to your `.env.local` file:

```env
YOUTUBE_API_KEY=your_youtube_api_key_here
```

### 3. Fallback Behavior

If no API key is configured:
- Server-side enhancement is skipped
- Client-side fetching returns "YouTube Video" as placeholder
- Original API duration is used if available
- System gracefully degrades without errors

## Usage

The integration works automatically:

1. **Server-side**: When video lists are fetched, YouTube durations are automatically enhanced
2. **Client-side**: Video cards display the duration or fetch it if needed
3. **Fallbacks**: Multiple fallback mechanisms ensure the app works without API keys

## API Limits

YouTube Data API v3 has quotas:
- 10,000 units per day by default
- Each video metadata request costs ~1-3 units
- Monitor usage in Google Cloud Console
- Consider caching results for production use

## Testing

Run the YouTube utility tests:

```bash
npm test lib/utils/__tests__/youtube.test.ts
```

## Future Improvements

1. **Caching**: Implement Redis/database caching for YouTube metadata
2. **Batch Processing**: Fetch multiple video durations in single API calls
3. **Background Jobs**: Process YouTube metadata asynchronously
4. **Error Handling**: More sophisticated error handling and retry logic
5. **Analytics**: Track which videos need duration fetching most often
