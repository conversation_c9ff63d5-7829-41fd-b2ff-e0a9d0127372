import {
  formatDuration,
  parseISO8601Duration,
  extractYouTubeVideoId,
  getYouTubeThumbnail,
  isValidVideoLength
} from '../youtube'

describe('YouTube Utilities', () => {
  describe('formatDuration', () => {
    it('should format seconds to MM:SS', () => {
      expect(formatDuration(65)).toBe('01:05')
      expect(formatDuration(125)).toBe('02:05')
      expect(formatDuration(30)).toBe('00:30')
    })

    it('should format hours correctly', () => {
      expect(formatDuration(3665)).toBe('1:01:05')
      expect(formatDuration(7200)).toBe('2:00:00')
    })
  })

  describe('parseISO8601Duration', () => {
    it('should parse ISO 8601 duration format', () => {
      expect(parseISO8601Duration('PT4M13S')).toBe(253)
      expect(parseISO8601Duration('PT1H30M45S')).toBe(5445)
      expect(parseISO8601Duration('PT2M')).toBe(120)
      expect(parseISO8601Duration('PT45S')).toBe(45)
    })

    it('should handle invalid format', () => {
      expect(parseISO8601Duration('invalid')).toBe(0)
      expect(parseISO8601Duration('')).toBe(0)
    })
  })

  describe('extractYouTubeVideoId', () => {
    it('should extract video ID from various YouTube URLs', () => {
      expect(extractYouTubeVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
      expect(extractYouTubeVideoId('https://youtu.be/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
      expect(extractYouTubeVideoId('https://www.youtube.com/embed/dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
      expect(extractYouTubeVideoId('dQw4w9WgXcQ')).toBe('dQw4w9WgXcQ')
    })

    it('should return null for invalid URLs', () => {
      expect(extractYouTubeVideoId('https://example.com')).toBeNull()
      expect(extractYouTubeVideoId('invalid')).toBeNull()
    })
  })

  describe('getYouTubeThumbnail', () => {
    it('should generate correct thumbnail URLs', () => {
      const videoId = 'dQw4w9WgXcQ'
      expect(getYouTubeThumbnail(videoId)).toBe('https://img.youtube.com/vi/dQw4w9WgXcQ/hqdefault.jpg')
      expect(getYouTubeThumbnail(videoId, 'maxres')).toBe('https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg')
    })
  })

  describe('isValidVideoLength', () => {
    it('should identify valid video lengths', () => {
      expect(isValidVideoLength('04:32')).toBe(true)
      expect(isValidVideoLength('1:23:45')).toBe(true)
      expect(isValidVideoLength('YouTube Video')).toBe(true)
    })

    it('should identify invalid video lengths', () => {
      expect(isValidVideoLength('00:00 to 00:00')).toBe(false)
      expect(isValidVideoLength('00:00')).toBe(false)
      expect(isValidVideoLength('N/A')).toBe(false)
      expect(isValidVideoLength('')).toBe(false)
      expect(isValidVideoLength('0:00')).toBe(false)
    })
  })
})
