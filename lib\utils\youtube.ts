/**
 * YouTube utility functions for video metadata extraction
 */

// Interface for YouTube video info
export interface YouTubeVideoInfo {
  duration: string
  title: string
  thumbnail: string
}

/**
 * Format duration from seconds to MM:SS or HH:MM:SS format
 */
export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * Parse ISO 8601 duration format (PT4M13S) to seconds
 */
export const parseISO8601Duration = (duration: string): number => {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
  if (!match) return 0

  const hours = parseInt(match[1] || '0', 10)
  const minutes = parseInt(match[2] || '0', 10)
  const seconds = parseInt(match[3] || '0', 10)

  return hours * 3600 + minutes * 60 + seconds
}

/**
 * Get YouTube video ID from various YouTube URL formats
 */
export const extractYouTubeVideoId = (url: string): string | null => {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
  ]

  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) return match[1]
  }

  return null
}

/**
 * Get YouTube video thumbnail URL
 */
export const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'medium' | 'high' | 'standard' | 'maxres' = 'high'): string => {
  const qualityMap = {
    default: 'default',
    medium: 'mqdefault',
    high: 'hqdefault',
    standard: 'sddefault',
    maxres: 'maxresdefault'
  }

  return `https://img.youtube.com/vi/${videoId}/${qualityMap[quality]}.jpg`
}

/**
 * Fetch YouTube video duration using oEmbed API (limited info)
 * This is a fallback method that doesn't require API keys but has limitations
 */
export const fetchYouTubeVideoInfo = async (videoId: string): Promise<YouTubeVideoInfo | null> => {
  try {
    // Try oEmbed API first (doesn't provide duration but confirms video exists)
    const oEmbedResponse = await fetch(
      `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`
    )

    if (!oEmbedResponse.ok) {
      throw new Error('Video not found or private')
    }

    const oEmbedData = await oEmbedResponse.json()

    // oEmbed doesn't provide duration, so we return basic info
    return {
      duration: '', // Duration not available through oEmbed
      title: oEmbedData.title || '',
      thumbnail: getYouTubeThumbnail(videoId)
    }
  } catch (error) {
    console.error('Error fetching YouTube video info:', error)
    return null
  }
}

/**
 * Alternative method to get video duration using YouTube Data API v3
 * This requires an API key but provides accurate duration
 * 
 * To use this method:
 * 1. Get a YouTube Data API v3 key from Google Cloud Console
 * 2. Add it to your environment variables as YOUTUBE_API_KEY
 * 3. Uncomment and use this function
 */
export const fetchYouTubeVideoInfoWithAPI = async (videoId: string, apiKey: string): Promise<YouTubeVideoInfo | null> => {
  try {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${apiKey}&part=contentDetails,snippet`
    )

    if (!response.ok) {
      throw new Error('Failed to fetch video data from YouTube API')
    }

    const data = await response.json()

    if (!data.items || data.items.length === 0) {
      throw new Error('Video not found')
    }

    const video = data.items[0]
    const durationISO = video.contentDetails.duration
    const durationSeconds = parseISO8601Duration(durationISO)

    return {
      duration: formatDuration(durationSeconds),
      title: video.snippet.title,
      thumbnail: getYouTubeThumbnail(videoId)
    }
  } catch (error) {
    console.error('Error fetching YouTube video info with API:', error)
    return null
  }
}

/**
 * Check if a video length string is valid (not empty or placeholder)
 */
export const isValidVideoLength = (length: string): boolean => {
  if (!length) return false
  
  const invalidPatterns = [
    '00:00 to 00:00',
    '00:00',
    'N/A',
    '',
    '0:00'
  ]
  
  return !invalidPatterns.includes(length.trim())
}

/**
 * Get display duration for a video, preferring API data over YouTube fetching
 */
export const getVideoDisplayDuration = async (
  video: { length: string; video_type: string; link: string }
): Promise<string> => {
  // If we have a valid length from the API, use it
  if (isValidVideoLength(video.length)) {
    return video.length
  }

  // For YouTube videos, try to get duration
  if (['2', '6'].includes(video.video_type) && video.link) {
    const videoId = extractYouTubeVideoId(video.link) || video.link
    
    // Try with API key if available (server-side only)
    if (typeof window === 'undefined' && process.env.YOUTUBE_API_KEY) {
      const info = await fetchYouTubeVideoInfoWithAPI(videoId, process.env.YOUTUBE_API_KEY)
      if (info?.duration) {
        return info.duration
      }
    }

    // Fallback: return a placeholder for YouTube videos
    return 'YouTube Video'
  }

  // Return original length or placeholder
  return video.length || 'N/A'
}
