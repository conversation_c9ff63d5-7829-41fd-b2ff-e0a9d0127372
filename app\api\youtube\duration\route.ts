import { NextRequest, NextResponse } from 'next/server'
import { fetchYouTubeVideoInfoWithAPI, extractYouTubeVideoId } from '@/lib/utils/youtube'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const videoId = searchParams.get('videoId')

    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    // Extract video ID if a full URL is provided
    const extractedVideoId = extractYouTubeVideoId(videoId) || videoId

    // Check if YouTube API key is available
    const apiKey = process.env.YOUTUBE_API_KEY
    if (!apiKey) {
      return NextResponse.json(
        { 
          error: 'YouTube API key not configured',
          duration: 'YouTube Video' // Fallback
        },
        { status: 200 }
      )
    }

    // Fetch video info using YouTube Data API
    const videoInfo = await fetchYouTubeVideoInfoWithAPI(extractedVideoId, apiKey)

    if (!videoInfo) {
      return NextResponse.json(
        { 
          error: 'Video not found or private',
          duration: 'YouTube Video' // Fallback
        },
        { status: 200 }
      )
    }

    return NextResponse.json({
      duration: videoInfo.duration,
      title: videoInfo.title,
      thumbnail: videoInfo.thumbnail
    })

  } catch (error) {
    console.error('Error fetching YouTube video duration:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch video duration',
        duration: 'YouTube Video' // Fallback
      },
      { status: 200 }
    )
  }
}
