import { Component, OnInit, ViewChild, Template<PERSON>ef, On<PERSON><PERSON>roy } from '@angular/core';
import { Location } from '@angular/common';
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import * as $ from 'jquery';

import { Tests2Service } from 'src/app/Services/Dashboard/tests-2.service';
import { TestsService } from '../../Services/tests.service';
import { ChaptersService } from '../../Services/Dashboard/chapters.service';
import { CompetitiveService } from '../../Services/Dashboard/competitive.service';
import { CompanyService } from '../../Services/Dashboard/company.service';
import { OpenTestsService } from '../../Services/open-tests.service';
import { SectionWisePapersService } from '../../Services/Dashboard/section-wise-papers.service';
import { TmcqService } from '../../Services/Dashboard/TMCQ/tmcq.service';

import { Question } from '../../Models/Dashboard/Chapters/Question';
import { CompetitiveQuestion } from '../../Models/Dashboard/Competitive/CompetitiveQuestion';
import { CompanyQuestion } from '../../Models/Dashboard/Company/CompanyQuestions';
import { Tests } from '../../Models/Tests';
import { PaperAdditionData } from '../../Models/PaperAddtionData';
import { CountDownTimer } from 'src/app/Utility/countDownTimer';

declare let gtag: Function;

@Component({
  selector: 'app-tests',
  templateUrl: './tests.component.html',
  styleUrls: ['./tests.component.scss']
})
export class TestsComponent implements OnInit, OnDestroy {

  @ViewChild('warnTemplate') public wTemplate: TemplateRef<any>;
  @ViewChild('marksTemplate') public mTemplate: TemplateRef<any>;
  @ViewChild('confTemplate') public confTemplate: TemplateRef<any>;

  public testType: number;
  public paperId: string;
  public paperName: string;
  public paperLim: number;

  public questions: Question[];
  public additionData: PaperAdditionData[];
  public competitiveQuestions: CompetitiveQuestion[];
  public companyQuestions: CompanyQuestion[];
  public modelQuestions: Tests['questions'];
  public aQns: Question[];
  public bPaperSubmitted = false;
  public showAns = true;
  public negMarks = false;
  public randQues = false;

  public mins: number;
  public secs: number;

  public marks = 0;
  public sectionMarks = [];
  public sectionTrack: any[];
  public noQues = 0;
  public answerId = '';

  public isSuperUser = false;
  public showAllExpl = false;

  public questionTexts = ['A. ', 'B. ', 'C. ', 'D. ', 'E. '];

  private email: string;

  public showIndicator = false;

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };
  public blurQuestions = false;

  public isCollapsed = [];

  public showPopoutTimer = false;
  private countDownTimer: any;

  constructor(private router: Router,
              private location: Location,
              private route: ActivatedRoute,
              private modalService: BsModalService,
              private testsService: TestsService,
              private tests2Service: Tests2Service,
              private chaptersService: ChaptersService,
              private competitiveService: CompetitiveService,
              private companyService: CompanyService,
              private openTestsService: OpenTestsService,
              private sectionWisePapersService: SectionWisePapersService,
              private tmcqService: TmcqService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-*********-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    this.showIndicator = true;
    this.route.paramMap.subscribe(params => {
      this.testType = parseInt(params.get('test_type'), 10);
      this.paperId = params.get('paper_id');
      this.paperName = params.get('paper_name');
      this.paperLim = parseInt(params.get('paper_lim'), 10) / (1000 * 60);
    });

    this.email = sessionStorage.getItem('QMail');

    // this.openModal(this.mTemplate);

    setTimeout(() => {
      if (this.paperLim > 0) {
        this.blurQuestions = true;
        this.openModal(this.confTemplate);
      }
    });

    this.checkSuperUser();

    const that = this;
    if (this.testType === 1) {
      // Chapter wise papers
      this.tests2Service.getQuestions(this.paperId).subscribe(response => {
        response.questions.forEach(val => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = 0;
          that.isCollapsed.push(true);
        });

        that.questions = response.questions;
        that.additionData = response.paper_addition_data;
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 2) {
      this.tests2Service.getCompetitiveQuestions(this.paperId).subscribe(response => {
        response.questions.forEach(val => {
          val.bAnswered = false;
          val.options = [val.opt_1, val.opt_2, val.opt_3, val.opt_4];
          val.selected_ans = 0;
          that.isCollapsed.push(true);
        });

        that.competitiveQuestions = response.questions;
        that.additionData = response.paper_addition_data;
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 3) {
      this.tests2Service.getCompanyQuestions(this.paperId).subscribe(response => {
        response.questions.forEach(val => {
          val.bAnswered = false;
          val.options = [val.opt_1, val.opt_2, val.opt_3, val.opt_4];
          val.selected_ans = 0;
          that.isCollapsed.push(true);
        });

        that.companyQuestions = response.questions;
        that.additionData     = response.paper_addition_data;
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 4) {
      this.testsService.getQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.modelQuestions = respObj.questions;
        that.showAns        = parseInt(respObj.paper_data.show_ans, 10) === 1 ? true : false;
        that.additionData   = respObj.paper_addition_data;

        respObj.questions.forEach((val: {
          bAnswered: boolean; options: any[]; option_1: any; option_2: any; option_3: any; option_4: any;
          selected_ans: string; }) => {
          val.bAnswered = false;
          val.options = [val.option_1, val.option_2, val.option_3, val.option_4];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 6) {
      this.tests2Service.getQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        this.questions    = respObj.questions;
        this.additionData = respObj.paper_addition_data;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any; opt_3: any; opt_4: any;
          opt_5: any; selected_ans: number; }) => { // Changed type to number
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = -1; // Initialize to -1 to match evaluation logic
          that.isCollapsed.push(true);
        });
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 5 || this.testType === 7) {
      this.openTestsService.getTrialQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        this.questions    = respObj.questions;
        this.additionData = respObj.paper_addition_data;
        this.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any; opt_3: any; opt_4: any;
          opt_5: any; selected_ans: string; }) => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });

        $('body').bind('cut copy select', (e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 8) {

      this.openTestsService.getOpenCompetitiveQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.questions    = respObj.questions;
        that.additionData = respObj.paper_addition_data;
        that.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;
        that.negMarks     = parseInt(respObj.paper_config.neg_marks, 10) === 1 ? true : false;
        that.randQues     = parseInt(respObj.paper_config.rand_ques, 10) === 1 ? true : false;

        that.questions.forEach((val: Question) => {
          val.bAnswered = false;
          val.original_qno = val.question_no;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = -1;
          that.isCollapsed.push(true);
        });

        if (this.randQues) {

          if (this.additionData) {
            this.questions = this.shuffleQuestions(this.questions, true, null, null);
          }
        }

        $('body').bind('cut copy select', (_e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 9) {

      this.sectionWisePapersService.getQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.questions    = respObj.questions;
        that.additionData = respObj.paper_addition_data;
        that.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any; opt_3: any; opt_4: any;
          opt_5: any; selected_ans: string; }) => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });

        $('body').bind('cut copy select', (e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 10) {

      this.sectionWisePapersService.getAfcatQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.questions    = respObj.questions;
        that.additionData = respObj.paper_addition_data;
        that.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any;
          opt_3: any; opt_4: any; opt_5: any; selected_ans: string; }) => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });

        $('body').bind('cut copy select', (e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 11) {

      this.tmcqService.getQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.questions    = respObj.questions;
        that.additionData = respObj.paper_addition_data;
        that.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any;
          opt_3: any; opt_4: any; opt_5: any; selected_ans: string; }) => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });

        $('body').bind('cut copy select', (e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    } else if (this.testType === 13 || this.testType === 14) {

      this.competitiveService.getCompetitivePaperQuestions(this.paperId).subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));

        that.questions    = respObj.questions;
        that.additionData = respObj.paper_addition_data;
        // that.showAns      = parseInt(respObj.paper_config.show_ans, 10) === 1 ? true : false;

        respObj.questions.forEach((val: {
          bAnswered: boolean; question: string; options: any[]; opt_1: any; opt_2: any;
          opt_3: any; opt_4: any; opt_5: any; selected_ans: string; }) => {
          val.bAnswered = false;

          val.question = this._sanitizeSupSubScripts(val.question);
          val.question = this._sanitizeImages(val.question);

          val.opt_1 = this._sanitizeSupSubScripts(val.opt_1);
          val.opt_2 = this._sanitizeSupSubScripts(val.opt_2);
          val.opt_3 = this._sanitizeSupSubScripts(val.opt_3);
          val.opt_4 = this._sanitizeSupSubScripts(val.opt_4);
          val.opt_5 = this._sanitizeSupSubScripts(val.opt_5);

          val.options      = [val.opt_1, val.opt_2, val.opt_3, val.opt_4, val.opt_5];
          val.selected_ans = '';
          that.isCollapsed.push(true);
        });

        $('body').bind('cut copy select', (e: any) => false);
        $('body').on('contextmenu', (e: any) => false);
        $('body').on('selectstart', (e: any) => false);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    }
  }

  _sanitizeSupSubScripts(val: string) {

    if (typeof val !== 'undefined' && val !== null) {
      if (val.match(/\^/g)) {
        val = val.split('^').join('<sup>');
        val = val.replace(/(<sup>(?:(?!<sup>).)*)<sup>((?:(?!<sup>).)*)/g, '$1</sup>$2');
      }

      if (val.match(/~/g)) {
        val = val.split('~').join('<sub>');
        val = val.replace(/(<sub>(?:(?!<sub>).)*)<sub>((?:(?!<sub>).)*)/g, '$1</sub>$2');
      }
    }

    return val;
  }

  _sanitizeImages(val: string) {

    if (typeof val !== 'undefined' && val !== null) {
      if (val.match(/.*@\dpx|\dpx/g)) {
        const split = val.split('![')[1].split('](')[1].split('@');
        const url = split[0];
        const height = split[1].split('|')[0];
        const width = split[1].split('|')[1].split(')')[0];

        val = val.split('![')[0] + '\r\r<img src="' + url + '" height="' + height + '" width="' + width + '" />';
      }
    }

    return val;
  }

  /**
   * Function to evaluate the answers after the student
   * finishes... or gives up on the test.
   *
   * Tallys the marks and records it to the db.
   *
   * @param bOVerride Override all answered check flag
   */
  evaluateAnswers(bOVerride: boolean) {
    let marks = 0;

    const that = this;

    this.showIndicator = true;
    if (this.questions && this.questions.length > 0) {

      let reqAdditionData: PaperAdditionData;

      this.sectionMarks = [];
      for (const question of this.questions) {
        if (bOVerride || question.bAnswered) {

          let key = '';
          let sectionMarks = 0;
          if (this.additionData && this.additionData.length > 0) {
            const tempReqAddData = this.additionData.find(x => x.type === 0 && x.question_no === parseInt(question.question_no, 10));

            if (tempReqAddData || reqAdditionData) {
              reqAdditionData = tempReqAddData || reqAdditionData;

              key = this.paperId + ':' + reqAdditionData.question_no.toString();

              if (this.sectionMarks[key]) {
                sectionMarks = this.sectionMarks[key];
              }
            }
          }

          if (question.selected_ans === parseInt(question.correct_opt, 10)) {
            marks += this.negMarks ? 2 : 1;

            if (reqAdditionData) {
              this.sectionMarks[key] = sectionMarks + (this.negMarks ? 2 : 1);
            }
          } else if (question.selected_ans !== -1 && this.negMarks) {
            marks -= 1;

            if (reqAdditionData) {
              this.sectionMarks[key] = sectionMarks - 1;
            }
          } else {
            if (reqAdditionData) {
              this.sectionMarks[key] = sectionMarks;
            }
          }
        } else {
          that.openModal(that.wTemplate);
          return;
        }
      }

      const reqBody = [];
      this.sectionTrack = [];
      if (this.sectionMarks && Object.keys(this.sectionMarks).length > 0) {
        let reqObj: { question_no: string, marks: number };

        Object.keys(this.sectionMarks).forEach(x => {
          reqObj = { question_no: '', marks: 1 };

          reqObj.question_no = x.split(':')[1];
          reqObj.marks       = this.sectionMarks[x];

          const addData = that.additionData.find(y => y.question_no === parseInt(reqObj.question_no, 10) && y.type === 0);

          if (addData) {
            const sectionData = { section_name: addData.value.replace(/[#]+/g, ''), marks: reqObj.marks };
            that.sectionTrack.push(sectionData);
          }

          reqBody.push(reqObj);
        });
      }

      this.noQues = this.questions.length;

      if (this.testType === 7) {

        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        const todayStr = mm + '/' + dd + '/' + yyyy;

        this.openTestsService.submitAnswers(this.email, this.paperId, marks, todayStr).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === '200') {
            this.marks = marks;

            this.answerId = resp.asnwer_id;
            if (reqBody && reqBody.length > 0) {
              this.submitSectionWiseMarks(reqBody);
            } else {
              this.openModal(this.mTemplate);
              this.showIndicator = false;
            }

            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });
      } else if (this.testType === 8) {
        this.openTestsService.submitOpenCompetitiveAnswers(this.email, this.paperId, marks).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === 'Answer Submitted') {
            this.marks = marks;

            this.answerId = resp.asnwer_id;
            if (reqBody && reqBody.length > 0) {
              this.submitSectionWiseMarks(reqBody);
            } else {
              this.openModal(this.mTemplate);
              this.showIndicator = false;
            }

            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });

      } else if (this.testType === 9 || this.testType === 10) {
        this.sectionWisePapersService.submitAnswers(this.email, this.paperId, marks).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === 'Answer Submitted') {
            this.marks = marks;

            this.answerId = resp.asnwer_id;
            if (reqBody && reqBody.length > 0) {
              this.submitSectionWiseMarks(reqBody);
            } else {
              this.openModal(this.mTemplate);
              this.showIndicator = false;
            }

            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });
      } else if (this.testType === 11) {
        this.tmcqService.submitMarks(this.paperId, this.email, marks).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === 'submitted') {
            this.marks = marks;
            this.openModal(this.mTemplate);
            this.showIndicator = false;
            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });
      }  else if (this.testType === 13 || this.testType === 14) {
        this.competitiveService.submitMarks(this.paperId, this.email, marks).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === 'Answer Submitted') {
            this.marks = marks;
            this.openModal(this.mTemplate);
            this.showIndicator = false;
            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });
      } else if (this.testType !== 5) {
        this.chaptersService.submitMarks(this.email, this.paperId, marks).subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));

          if (resp.text === 'Answer Submitted') {
            this.showIndicator = false;
            this.marks = marks;
            this.openModal(this.mTemplate);

            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          this.showIndicator = false;
        });
      } else {
        const today = new Date();
        const dd = String(today.getDate()).padStart(2, '0');
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const yyyy = today.getFullYear();

        const todayStr = mm + '/' + dd + '/' + yyyy;
        this.marks = marks;

        this.testsService.submitAnswers(sessionStorage.getItem('QMail'), this.paperId, this.marks, todayStr).subscribe(response => {
          const respText = JSON.parse(JSON.stringify(response));

          if (respText.text === 'Answer Submitted') {
            this.marks = marks;

            this.answerId = respText.asnwer_id;
            if (reqBody && reqBody.length > 0) {
              this.submitSectionWiseMarks(reqBody);
            } else {
              this.openModal(this.mTemplate);
              this.showIndicator = false;
            }

            this.format(0, 0);
            this.countDownTimer.stop();
          }
        }, error => {
          console.log(error.error);
        });
      }

    } else if (this.competitiveQuestions && this.competitiveQuestions.length > 0) {

      for (const question of this.competitiveQuestions) {
        if (bOVerride || question.bAnswered) {
          if (question.selected_ans === parseInt(question.correct_opt, 10)) {
            marks++;
          }
        } else {
          that.openModal(that.wTemplate);
          return;
        }
      }

      this.noQues = this.competitiveQuestions.length;

      this.competitiveService.submitMarks(this.email, this.paperId, marks).subscribe(response => {
        const resp = JSON.parse(JSON.stringify(response));

        if (resp.text === 'Answer Submitted') {
          this.showIndicator = false;
          this.marks = marks;
          this.openModal(this.mTemplate);

          this.format(0, 0);
          this.countDownTimer.stop();
        }
      }, error => {

      });
    } else if (this.companyQuestions && this.companyQuestions.length > 0) {

      for (const question of this.companyQuestions) {
        if (bOVerride || question.bAnswered) {
          if (question.selected_ans === parseInt(question.correct_opt, 10)) {
            marks++;
          }
        } else {
          that.openModal(that.wTemplate);
          return;
        }
      }

      this.noQues = this.companyQuestions.length;

      this.companyService.submitMarks(this.email, this.paperId, marks).subscribe(response => {
        const resp = JSON.parse(JSON.stringify(response));

        if (resp.text === 'Answer Submitted') {
          this.showIndicator = false;
          this.marks = marks;
          this.openModal(this.mTemplate);

          this.format(0, 0);
          this.countDownTimer.stop();
        }
      }, error => {

      });
    } else if (this.modelQuestions && this.modelQuestions.length > 0) {

      for (const question of this.modelQuestions) {
        if (bOVerride || question.bAnswered) {
          if (question.selected_ans === parseInt(question.correct_opt, 10)) {
            marks++;
          }
        } else {
          that.openModal(that.wTemplate);
          return;
        }
      }

      this.noQues = this.modelQuestions.length;

      const today = new Date();
      const dd = String(today.getDate()).padStart(2, '0');
      const mm = String(today.getMonth() + 1).padStart(2, '0');
      const yyyy = today.getFullYear();

      const todayStr = mm + '/' + dd + '/' + yyyy;

      this.testsService.submitAnswers(sessionStorage.getItem('QMail'), this.paperId, marks, todayStr).subscribe(response => {
        const respText = JSON.parse(JSON.stringify(response));

        if (respText.text === 'Answer Submitted') {
          this.marks = marks;
          this.showIndicator = false;
          this.openModal(this.mTemplate);

          this.format(0, 0);
          this.countDownTimer.stop();
        }
      }, error => {
        console.log(error.error);
      });
    }

    // Boolean to indicate the paper has been submitted or not
    this.bPaperSubmitted = true;
  }

  startTest(): void {

    this.blurQuestions = false;

    this.countDownTimer = new CountDownTimer(this.paperLim * 60, 1000);
    const timeObj = CountDownTimer.parse(this.paperLim * 60);

    this.format(timeObj.minutes, timeObj.seconds);

    this.countDownTimer.onTick(this.format).onTick(this.checkTime.bind(this));

    this.countDownTimer.start();

    if (this.paperLim > 0) {
      if (window.innerWidth <= 440) {
        this.showPopoutTimer = true;
      }

      $(window).scroll(() => {
        if ($('.time-box').offset().top - $(window).scrollTop() < $('.time-box').height()) {
          this.showPopoutTimer = true;
        } else {
          this.showPopoutTimer = false;
        }
      });
    }

    this.modalRef.hide();
  }

  /**
   * Function to handle submission of section wise marks
   * of a paper if sections exist on that paper.
   */
  submitSectionWiseMarks(reqBody: any[]) {

    if (this.testType === 5 || this.testType === 7) {
      this.openTestsService.submitSectionAnswers(sessionStorage.getItem('QMail'), this.paperId,
        this.answerId, reqBody).subscribe(response => {

        const respText = JSON.parse(JSON.stringify(response));

        if (respText.text === 'Created') {
          this.showIndicator = false;
          this.openModal(this.mTemplate);
        }
      }, error => {

      });
    } else if (this.testType === 9) {
      this.sectionWisePapersService.submitSectionAnswers(sessionStorage.getItem('QMail'), this.paperId,
        this.answerId, reqBody).subscribe(response => {

        const respText = JSON.parse(JSON.stringify(response));

        if (respText.text === 'Created') {
          this.showIndicator = false;
          this.openModal(this.mTemplate);
        }
      }, error => {

      });
    }
  }

  shuffleQuestions(questions: Question[], bAll: boolean, startIdx: number, endIdx: number) {

    let currIdx = bAll ? questions.length : endIdx;

    while ((bAll ? 0 : startIdx) !== currIdx) {

      let randomIdx = Math.floor(Math.random() * currIdx);

      while (!bAll && randomIdx < startIdx - 1) {
        randomIdx = Math.floor(Math.random() * currIdx);
      }

      currIdx -= 1;

      const tempQues = questions[currIdx];
      questions[currIdx] = questions[randomIdx];
      questions[randomIdx] = tempQues;

      questions[currIdx].question_no = currIdx.toString();
      questions[randomIdx].question_no = randomIdx.toString();
    }

    const question = questions.find(x => x.question_no === '0');
    if (question) {
      question.question_no = questions.length.toString();
    }

    questions.sort((a, b) => {
      const aNo = parseInt(a.question_no, 10);
      const bNo = parseInt(b.question_no, 10);

      if (aNo < bNo) return -1;
      else if (aNo > bNo) return 1;
      else return 0;
    });

    return questions;
  }

  format(minutes: string | number, seconds: string | number) {

    minutes = minutes < 10 ? '0' + minutes : minutes;
    seconds = seconds < 10 ? '0' + seconds : seconds;
    $('.timeRem').text(minutes + ' : ' + seconds);

    if (minutes < 5) {
      $('.timer-box').css('color', '#FF0000');
    } else if (minutes < 10) {
      $('.timer-box').css('color', '#FB6E00');
    } else {
      $('.timer-box').css('color', '#1BAB00');
    }
  }

  checkTime(minutes: string | number, seconds: string | number) {

    if (minutes === 0 && seconds === 0) {
      this.evaluateAnswers(true);
    }
  }

  checkSuperUser() {
    const email = sessionStorage.getItem('QMail');

    if (email === '<EMAIL>' || email === '<EMAIL>'
        || email === '<EMAIL>' || email === '<EMAIL>') {
          this.isSuperUser = true;
    }
  }

  scrollToQuestion(ques_no: string) {
    const id = 'qm-q--' + ques_no;

    $( 'html, body' ).animate({ scrollTop: $( '#' + id ).offset().top - 30 }, 500);
  }

  goBack() {
    this.location.back();
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  confirm() {
    this.modalRef.hide();
    this.evaluateAnswers(true);
  }

  ngOnDestroy() {
    if (this.modalRef) {
      try {
        this.countDownTimer.stop();
      } catch (error) {
        console.warn('The website had an oopsie!');
      }

      this.modalRef.hide();
    }
  }
}
